-- Assignment System Schema Updates
-- Run this in your Supabase SQL editor

-- 1. Update problems table to support multiple standards
ALTER TABLE problems 
ALTER COLUMN standard_code TYPE TEXT[] USING ARRAY[standard_code];

-- 2. Add target_standard and is_template fields to assignments table
ALTER TABLE assignments 
ADD COLUMN IF NOT EXISTS target_standard TEXT,
ADD COLUMN IF NOT EXISTS is_template BOOLEAN DEFAULT false;

-- 3. Create index for efficient standard_code queries
CREATE INDEX IF NOT EXISTS idx_problems_standard_code_gin ON problems USING GIN(standard_code);

-- 4. Insert sample problems for expressions_equations MVP

-- Q1: Fixed mechanical problem (same for all students)
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '11111111-1111-1111-1111-111111111111',
    'Q1: Basic Linear Equation',
    'Solve for x: 3x + 7 = 22',
    'problem_mechanical',
    ARRAY['expressions_equations'],
    3,
    '[]',
    '{"x": "5"}',
    '{"answer": "x = 5", "variables": ["x"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- Q3: Fixed socratic problem (same for all students)
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '33333333-3333-3333-3333-333333333333',
    'Q3: System of Equations Analysis',
    'Solve the system: 2x + y = 8 and x - y = 1. Explain your reasoning at each step.',
    'socratic_conceptual',
    ARRAY['expressions_equations'],
    6,
    '[
        "Choose a method (substitution or elimination)",
        "Apply the chosen method to eliminate one variable",
        "Solve for the remaining variable",
        "Substitute back to find the other variable",
        "Verify your solution"
    ]',
    '{"x": "3", "y": "2"}',
    '{"answer": "x = 3, y = 2", "variables": ["x", "y"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- Q2 Options: Personalized mechanical problems (different combinations)

-- expressions_equations + expressions_equations (fallback)
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '22222222-2222-2222-2222-222222222222',
    'Q2: Advanced Expression Simplification',
    'Simplify: 4(2x - 3) + 5x = 3x + 10',
    'problem_mechanical',
    ARRAY['expressions_equations', 'expressions_equations'],
    5,
    '[]',
    '{"x": "2"}',
    '{"answer": "x = 2", "variables": ["x"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- expressions_equations + number_systems
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '22222222-2222-2222-2222-222222222223',
    'Q2: Rational Number Equations',
    'Solve for x: (2/3)x + 1.5 = 4.5',
    'problem_mechanical',
    ARRAY['expressions_equations', 'number_systems'],
    5,
    '[]',
    '{"x": "4.5"}',
    '{"answer": "x = 4.5", "variables": ["x"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- expressions_equations + geometry
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '22222222-2222-2222-2222-222222222224',
    'Q2: Perimeter Equation',
    'A rectangle has length (x + 5) and width (x - 2). If the perimeter is 26, find x.',
    'problem_mechanical',
    ARRAY['expressions_equations', 'geometry'],
    6,
    '[]',
    '{"x": "4.5"}',
    '{"answer": "x = 4.5", "variables": ["x"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- expressions_equations + functions
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '22222222-2222-2222-2222-222222222225',
    'Q2: Function Equation',
    'If f(x) = 3x - 7 and f(x) = 14, find the value of x.',
    'problem_mechanical',
    ARRAY['expressions_equations', 'functions'],
    5,
    '[]',
    '{"x": "7"}',
    '{"answer": "x = 7", "variables": ["x"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- Create template assignment
INSERT INTO assignments (
    id,
    title,
    description,
    assignment_type,
    target_standard,
    is_template,
    socratic_mode_enabled,
    created_by
) VALUES (
    'template-expressions-equations-001',
    'Expressions & Equations Practice',
    'Practice solving equations and working with expressions. This assignment adapts to your learning needs.',
    'practice',
    'expressions_equations',
    true,
    true,
    (SELECT id FROM users WHERE role = 'teacher' LIMIT 1)
) ON CONFLICT (id) DO UPDATE SET
    target_standard = EXCLUDED.target_standard,
    is_template = EXCLUDED.is_template;

-- Verify the data
SELECT 
    title,
    problem_type,
    standard_code,
    array_length(standard_code, 1) as standard_count
FROM problems 
WHERE standard_code && ARRAY['expressions_equations']
ORDER BY problem_type, array_length(standard_code, 1);
