"use client";

import { ReactNode, useEffect, useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { useIntakeForm } from "@/contexts/intake-form-context";
import { getStudentProfileData } from "@/app/actions/get-user-profile";
import ProtectedRoute from "@/components/protected-route";
import {
  UserIcon,
  BookOpenIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon,
  BellIcon,
  Bars3Icon,
  XMarkIcon,
} from "@heroicons/react/24/outline";

interface SidebarItemProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  isActive: boolean;
  isPlaceholder?: boolean;
}

const SidebarItem = ({
  href,
  icon: Icon,
  label,
  isActive,
  isPlaceholder,
}: SidebarItemProps) => {
  const baseClasses =
    "flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200";
  const activeClasses = "bg-blue-100 text-blue-700 border-r-2 border-blue-700";
  const inactiveClasses = "text-gray-600 hover:bg-gray-100 hover:text-gray-900";
  const placeholderClasses = "text-gray-400 cursor-not-allowed";

  const content = (
    <>
      <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
      {label}
      {isPlaceholder && (
        <span className="ml-auto text-xs bg-gray-200 text-gray-500 px-2 py-1 rounded">
          Soon
        </span>
      )}
    </>
  );

  if (isPlaceholder) {
    return (
      <div className={`${baseClasses} ${placeholderClasses}`}>{content}</div>
    );
  }

  return (
    <Link
      href={href}
      className={`${baseClasses} ${isActive ? activeClasses : inactiveClasses}`}
    >
      {content}
    </Link>
  );
};

export default function StudentLayout({ children }: { children: ReactNode }) {
  const { user, signOut } = useAuth();
  const { formData } = useIntakeForm();
  const router = useRouter();
  const pathname = usePathname();
  const [profileName, setProfileName] = useState<string | null>(null);
  const [isLoadingName, setIsLoadingName] = useState<boolean>(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    async function fetchNameForHeader() {
      if (user) {
        setIsLoadingName(true);
        if (formData.name) {
          setProfileName(formData.name);
          setIsLoadingName(false);
        } else {
          const profileResult = await getStudentProfileData();
          if (profileResult.success && profileResult.data?.name) {
            setProfileName(profileResult.data.name);
          } else {
            console.error(
              "Failed to fetch user name for header:",
              profileResult.error
            );
          }
          setIsLoadingName(false);
        }
      }
    }
    fetchNameForHeader();
  }, [user, formData.name]);

  const handleSignOut = async () => {
    await signOut();
    router.push("/");
  };

  const sidebarItems = [
    {
      href: "/student/profile",
      icon: UserIcon,
      label: "Your Profile",
      isPlaceholder: true,
    },
    {
      href: "/student/assignments",
      icon: BookOpenIcon,
      label: "Assignments",
      isPlaceholder: false,
    },
    {
      href: "/student/progress",
      icon: ChartBarIcon,
      label: "Your Progress",
      isPlaceholder: true,
    },
    {
      href: "/student/settings",
      icon: Cog6ToothIcon,
      label: "Settings",
      isPlaceholder: true,
    },
    {
      href: "/student/help",
      icon: QuestionMarkCircleIcon,
      label: "Help & Support",
      isPlaceholder: true,
    },
  ];

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Mobile sidebar overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div
          className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
            sidebarOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          <div className="flex h-full flex-col">
            {/* Logo and close button */}
            <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200">
              <Link href="/student/dashboard" className="flex items-center">
                <button className="logoButton text-lg">EdEngage</button>
              </Link>
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-6 space-y-2">
              <div className="mb-6">
                <h3 className="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Main Navigation
                </h3>
              </div>
              {sidebarItems.map((item) => (
                <SidebarItem
                  key={item.href}
                  href={item.href}
                  icon={item.icon}
                  label={item.label}
                  isActive={pathname === item.href}
                  isPlaceholder={item.isPlaceholder}
                />
              ))}
            </nav>

            {/* User info at bottom */}
            <div className="border-t border-gray-200 p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <UserIcon className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <div className="ml-3 flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {isLoadingName ? "Loading..." : profileName || user?.email}
                  </p>
                  <p className="text-xs text-gray-500">Student</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:pl-64">
          {/* Top header */}
          <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
            <div className="flex h-16 items-center justify-between px-4 sm:px-6">
              <div className="flex items-center">
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
                >
                  <Bars3Icon className="h-6 w-6" />
                </button>
                <h1 className="ml-2 lg:ml-0 text-xl font-semibold text-gray-900">
                  {pathname === "/student/dashboard" && "Dashboard"}
                  {pathname === "/student/assignments" && "Assignments"}
                  {pathname === "/student/profile" && "Your Profile"}
                  {pathname === "/student/progress" && "Your Progress"}
                  {pathname.includes("/student/assignments/") &&
                    "Assignment Details"}
                </h1>
              </div>
              <div className="flex items-center space-x-4">
                <button className="p-2 text-gray-400 hover:text-gray-600">
                  <BellIcon className="h-5 w-5" />
                </button>
                <button
                  onClick={handleSignOut}
                  className="rounded-md bg-red-600 px-3 py-1.5 text-sm text-white hover:bg-red-700 transition-colors"
                >
                  Sign Out
                </button>
              </div>
            </div>
          </header>

          {/* Page content */}
          <main className="flex-1">{children}</main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
