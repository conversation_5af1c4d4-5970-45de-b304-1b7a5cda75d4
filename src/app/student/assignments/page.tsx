// src/app/student/assignments/page.tsx
"use client";

import ProtectedRoute from "@/components/protected-route";
import { mockAssignments } from "@/lib/assignments";
import type { Assignment } from "../../../types/assignments";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import { useIntakeForm } from "@/contexts/intake-form-context";
import { useRouter } from "next/navigation";
import { ArrowRightIcon, BookOpenIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { getStudentProfileData } from "@/app/actions/get-user-profile";

const AssignmentCard = ({ assignment }: { assignment: Assignment }) => {
  return (
    <Link href={`/student/assignments/${assignment.id}`} passHref>
      <div className="block p-6 bg-white rounded-xl border border-gray-200 shadow-lg hover:shadow-xl transition-shadow transition-transform duration-200 cursor-pointer hover:-translate-y-1">
        <div className="flex items-center mb-4">
          <div className="p-2 bg-blue-100 rounded-full mr-4">
            <BookOpenIcon className="h-7 w-7 text-blue-600" />
          </div>
          <h2 className="text-xl font-semibold tracking-tight text-gray-800">
            {assignment.title}
          </h2>
        </div>
        <p className="font-normal text-gray-600 mb-5 line-clamp-2">
          {assignment.description}
        </p>
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-500">
            {assignment.questions.length} Questions • Grade{" "}
            {assignment.gradeLevel}
          </span>
          <div className="flex items-center text-blue-600 font-medium group">
            Start Assignment
            <ArrowRightIcon className="h-4 w-4 ml-1.5 transition-transform group-hover:translate-x-1" />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default function AssignmentsPage() {
  return (
    <div className="p-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Your Assignments
        </h1>
        <p className="text-gray-600">
          Complete your assignments to track your learning progress.
        </p>
      </div>

      {/* Assignments Grid */}
      {mockAssignments.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockAssignments.map((assignment) => (
            <AssignmentCard key={assignment.id} assignment={assignment} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-xl shadow-sm border border-gray-200">
          <BookOpenIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <p className="text-xl text-gray-600">
            No assignments available at the moment.
          </p>
          <p className="text-gray-500 mt-1">Please check back later.</p>
        </div>
      )}
    </div>
  );
}
