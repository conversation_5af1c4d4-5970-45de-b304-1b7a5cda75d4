"use client";
import React, { useEffect, useState, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import ProtectedRoute from "@/components/protected-route";
import { getAssignmentById } from "@/lib/assignments";
import { voteOnAIResponse } from "@/app/actions/vote-on-ai-response";
import type {
  Assignment,
  //AssignmentQuestion,
  ChatMessage,
  StudentAssignmentAnswers,
  StepInputDefinition,
} from "../../../../types/assignments";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import { api } from "@/utils/trpc-provider";
import {
  HomeIcon,
  CheckIcon,
  SparklesIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  Bars3Icon,
  ArrowUpIcon,
  ArrowRightIcon,
  HandThumbDownIcon as HandThumbsDownFilled,
  HandThumbUpIcon as HandThumbsUpFilled,
} from "@heroicons/react/24/solid";

import {
  HandThumbDownIcon,
  HandThumbUpIcon,
} from "@heroicons/react/24/outline";

const thumbClass = "h-4 w-4 text-blue-800 hover:text-blue-950 cursor-pointer";

function MessageComponent({ msg, firstInitial }) {
  const [vote, setVote] = useState<boolean | null>(
    msg.student_vote !== undefined ? msg.student_vote : null
  );
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [description, setDescription] = useState<string>("");

  function upvote() {
    setVote(true);
    voteOnAIResponse(msg.id, true, null);
  }

  function downvote() {
    setShowPopup(true);
  }

  function submitDownvote() {
    setShowPopup(false);
    setVote(false);
    voteOnAIResponse(msg.id, false, description);
    setDescription("");
  }

  function undoVote() {
    setVote(null);
    voteOnAIResponse(msg.id, null, null);
  }

  return (
    <div
      className={`relative flex items-center gap-3 ${
        msg.sender === "student" ? "justify-end" : ""
      }`}
    >
      {msg.sender === "EDEN" && showPopup && (
        <div className="flex flex-col absolute bottom-0 left-0 bg-gray-100/95 rounded-lg px-4 py-3 items-center">
          <input
            placeholder="What is unhelpful about this response?"
            className="w-xs mb-2"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
          />
          <button
            onClick={submitDownvote}
            className="bg-sky-100 py-1 px-2 rounded-full hover:bg-sky-300 cursor-pointer"
          >
            Submit feedback
          </button>
        </div>
      )}
      {msg.sender === "EDEN" && (
        <div className="flex flex-col">
          <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0">
            <SparklesIcon className="h-4 w-4 text-white" />
          </div>
          <div className="flex w-full justify-center">
            {vote === true ? (
              <HandThumbsUpFilled className={thumbClass} onClick={undoVote} />
            ) : (
              <HandThumbUpIcon className={thumbClass} onClick={upvote} />
            )}
            {vote === false ? (
              <HandThumbsDownFilled className={thumbClass} onClick={undoVote} />
            ) : (
              <HandThumbDownIcon className={thumbClass} onClick={downvote} />
            )}
          </div>
        </div>
      )}

      <div
        className={`px-4 py-2 rounded-lg max-w-[70%] ${
          msg.sender === "EDEN"
            ? "bg-gray-100 text-gray-800"
            : msg.sender === "student"
            ? msg.stepId
              ? "bg-green-600 text-white"
              : "bg-blue-600 text-white"
            : "bg-gray-50 text-gray-700"
        }`}
      >
        {msg.text}
      </div>
      {msg.sender === "student" && (
        <div className="w-8 h-8 rounded-full bg-orange-400 text-white flex items-center justify-center flex-shrink-0 text-sm font-medium">
          {/*can add an avatar too */}
          {firstInitial || "S"}{" "}
        </div>
      )}
    </div>
  );
}

function StepItem({
  step,
  isCompleted,
  answer,
}: {
  step: StepInputDefinition;
  isCompleted: boolean;
  answer: string;
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  const completeColor = isCompleted
    ? "bg-green-50 border-green-200"
    : "bg-gray-50 border-gray-200";
  return (
    <div className={`rounded-lg px-2 py-3 mb-2 border ${completeColor}`}>
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setIsExpanded((prev) => !prev)}
      >
        <div className="flex items-center gap-2">
          {isCompleted ? (
            <CheckIcon className="h-4 w-4 text-green-600" />
          ) : (
            <ArrowRightIcon className="h-4 w-4 text-gray-600" />
          )}
          <span className="text-sm font-medium text-gray-700">
            {step.label}
          </span>
        </div>
        {isExpanded ? (
          <ChevronDownIcon className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronUpIcon className="h-4 w-4 text-gray-500" />
        )}
      </div>
      {isExpanded && answer && (
        <div className="mt-2 pl-6 text-sm text-gray-600">{answer}</div>
      )}
    </div>
  );
}

// Interface for database problem
interface DatabaseProblem {
  id: string;
  title: string;
  statement: string;
  microservice_payload: {
    statement: string;
    answer: string;
    steps: string[];
  };
}

// Interface for session
interface Session {
  id: string;
  user_id: string;
  problem_id: string;
  created_at: string;
}

export default function AssignmentInstancePage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const assignmentId = params.assignmentId as string;
  const [assignment, setAssignment] = useState<Assignment | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentStepInQuestion, setCurrentStepInQuestion] = useState(0);
  const [studentAnswers, setStudentAnswers] =
    useState<StudentAssignmentAnswers>({});
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState("");
  const [chatLoading, setChatLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showStepSidebar, setShowStepSidebar] = useState<boolean>(true);
  const [currentStepInput, setCurrentStepInput] = useState("");
  const [currentProblem, setCurrentProblem] = useState<DatabaseProblem | null>(
    null
  );
  const [currentSession, setCurrentSession] = useState<Session | null>(null);
  const [finalAnswer, setFinalAnswer] = useState("");
  const [answerValidation, setAnswerValidation] = useState<{
    isCorrect: boolean | null;
    message: string;
  }>({ isCorrect: null, message: "" });
  const chatEndRef = useRef<HTMLDivElement>(null);
  const sessionCreatedRef = useRef(false);

  // tRPC queries and mutations
  const { data: problems } = api.chat.getProblems.useQuery();

  const createSessionMutation = api.chat.getOrCreateSession.useMutation({
    onSuccess: (session) => {
      setCurrentSession(session);
      refetchChatHistory();
    },
  });

  const sendChatMutation = api.chat.sendChatMessage.useMutation({
    onSuccess: async (data) => {
      console.log("Chat message sent successfully:", data);
      setChatInput("");
      // Add a small delay to ensure database consistency before refetching
      setTimeout(() => {
        refetchChatHistory();
      }, 500);
    },
    onError: (error) => {
      console.error("Error sending chat message:", error);
    },
  });

  const { data: chatHistoryData, refetch: refetchChatHistory } =
    api.chat.getChatHistory.useQuery(
      {
        sessionId: currentSession?.id || "",
        problemId: currentProblem?.id,
      },
      { enabled: !!currentSession?.id }
    );

  // Convert database chat messages to UI format
  useEffect(() => {
    if (chatHistoryData) {
      console.log("Chat history updated:", chatHistoryData);
      const convertedMessages: ChatMessage[] = chatHistoryData.map(
        (msg: {
          id: string;
          sender_type: string;
          content: string;
          timestamp: string;
          student_vote: boolean | null;
        }) => ({
          id: msg.id,
          sender: msg.sender_type === "student" ? "student" : "EDEN",
          text: msg.content,
          timestamp: new Date(msg.timestamp),
          student_vote: msg.student_vote,
        })
      );
      setChatMessages(convertedMessages);
    }
  }, [chatHistoryData]);

  // Load problem from database and create session
  useEffect(() => {
    if (
      problems &&
      problems.length > 0 &&
      !currentProblem &&
      !sessionCreatedRef.current
    ) {
      // Find the Linear Equation Practice problem
      const linearProblem = problems.find(
        (p) => p.title === "Linear Equation Practice"
      );
      if (linearProblem) {
        setCurrentProblem(linearProblem);
        sessionCreatedRef.current = true;
        // Create a session for this specific problem
        createSessionMutation.mutate({ problemId: linearProblem.id });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [problems, currentProblem]);

  useEffect(() => {
    if (assignmentId) {
      const loaded = getAssignmentById(assignmentId);
      if (loaded) {
        setAssignment(loaded);
        const answers: StudentAssignmentAnswers = {};
        loaded.questions.forEach((q) => {
          answers[q.id] = { steps: {}, final: {} };
          q.steps.forEach((step) => {
            answers[q.id].steps[step.id] = "";
          });
          q.variables.forEach((varName) => {
            answers[q.id].final[varName] = "";
          });
        });
        setStudentAnswers(answers);
        setCurrentQuestionIndex(0);

        // For mechanical problems, start at final answer stage
        const firstQuestion = loaded.questions[0];
        if (firstQuestion.problem_type === "problem_mechanical") {
          setCurrentStepInQuestion(firstQuestion.steps.length);
        } else {
          setCurrentStepInQuestion(0);
        }

        setCurrentStepInput("");

        // Don't set initial chat messages here - they'll come from the database
      } else {
        router.push("/student/assignments");
      }
    }
  }, [assignmentId, router]);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatMessages]);

  if (!assignment) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-16 w-16 animate-spin rounded-full border-4 border-gray-200 border-t-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading assignment...</p>
        </div>
      </div>
    );
  }

  const questions = assignment.questions;
  const totalQuestions = questions.length;
  const currentQ = questions[currentQuestionIndex];
  const steps = currentQ.steps;
  const finalVars = currentQ.variables;
  const isFinalAnswerStage = currentStepInQuestion >= steps.length;
  const currentStep = steps[currentStepInQuestion];

  // Determine if steps should be shown based on problem type
  const shouldShowSteps = currentQ.problem_type === "socratic_conceptual";

  //const processedProblemStatement = currentQ.problemStatement
  // .replace(/{vars\[0\]}/g, currentQ.variables[0])
  // .replace(/{vars\[1\]}/g, currentQ.variables[1]);

  const quickPrompts = [
    { icon: "🤔", text: "Where do I start?" },
    { icon: "🤷", text: "I'm stuck on this step" },
    { icon: "💡", text: "Can you give me a hint?" },
    { icon: "🔍", text: "Explain this concept" },
  ];

  async function handleChatSend(userMsg: string) {
    if (!userMsg.trim() || !currentSession || !currentProblem) return;

    setChatLoading(true);

    // Add user message optimistically
    const tempUserMessage: ChatMessage = {
      id: `temp-user-${Date.now()}`,
      sender: "student",
      text: userMsg.trim(),
      timestamp: new Date(),
    };
    setChatMessages((prev) => [...prev, tempUserMessage]);
    setChatInput("");

    try {
      await sendChatMutation.mutateAsync({
        sessionId: currentSession.id,
        problemId: currentProblem.id,
        userMessage: userMsg.trim(),
      });
    } catch (error) {
      console.error("Chat error:", error);
      // Add error message
      setChatMessages((prev) => [
        ...prev,
        {
          id: `error-${Date.now()}`,
          sender: "EDEN",
          text: "Sorry, I'm having trouble connecting. Please try again.",
          timestamp: new Date(),
        },
      ]);
    }

    setChatLoading(false);
  }

  function handleStepSubmit() {
    if (!currentStepInput.trim() || isFinalAnswerStage) return;
    const stepId = currentStep.id;
    setStudentAnswers((prev) => ({
      ...prev,
      [currentQ.id]: {
        ...prev[currentQ.id],
        steps: { ...prev[currentQ.id].steps, [stepId]: currentStepInput },
      },
    }));
    setChatMessages((msgs) => [
      ...msgs,
      {
        id: `${Date.now()}_${Math.random()}`,
        sender: "student",
        text: currentStepInput.trim(),
        timestamp: new Date(),
        stepId: String(currentStepInQuestion),
      },
    ]);
    setCurrentStepInput("");
    toNextStep();
  }

  function toNextStep() {
    if (currentStepInQuestion < steps.length - 1) {
      setCurrentStepInQuestion((i) => i + 1);
    } else {
      setCurrentStepInQuestion(steps.length);
    }
  }

  function handleFinalChange(varname: string, value: string) {
    setStudentAnswers((prev) => ({
      ...prev,
      [currentQ.id]: {
        ...prev[currentQ.id],
        final: { ...prev[currentQ.id].final, [varname]: value },
      },
    }));
  }

  function handleAnswerValidation() {
    if (!currentProblem || !finalAnswer.trim()) {
      setAnswerValidation({
        isCorrect: null,
        message: "Please enter an answer to validate.",
      });
      return;
    }

    // Simple string comparison with expected answer
    const expectedAnswer = currentProblem.microservice_payload.answer;
    const studentAnswer = finalAnswer.trim();

    // Normalize answers for comparison (remove spaces, convert to lowercase)
    const normalizedExpected = expectedAnswer.replace(/\s/g, "").toLowerCase();
    const normalizedStudent = studentAnswer.replace(/\s/g, "").toLowerCase();

    const isCorrect = normalizedExpected === normalizedStudent;

    setAnswerValidation({
      isCorrect,
      message: isCorrect
        ? "Correct! Well done!"
        : `Incorrect. The expected answer is: ${expectedAnswer}`,
    });

    // Add validation result to chat
    setChatMessages((prev) => [
      ...prev,
      {
        id: `validation-${Date.now()}`,
        sender: "EDEN",
        text: isCorrect
          ? "🎉 Excellent work! Your answer is correct!"
          : `That's not quite right. The correct answer is ${expectedAnswer}. Would you like me to explain how to get there?`,
        timestamp: new Date(),
      },
    ]);
  }

  function handleFinalSubmit() {
    if (
      !finalVars.every((v) => studentAnswers[currentQ.id]?.final[v]?.trim())
    ) {
      setChatMessages((msgs) => [
        ...msgs,
        {
          id: `${Date.now()}_${Math.random()}`,
          sender: "EDEN",
          text: "Please provide answers for all variables before continuing.",
          timestamp: new Date(),
        },
      ]);
      return;
    }
    if (currentQuestionIndex === totalQuestions - 1) {
      submitAssignment();
    } else {
      navigateToQuestion(currentQuestionIndex + 1);
    }
  }

  async function submitAssignment() {
    setIsSubmitting(true);
    await new Promise((res) => setTimeout(res, 1500));
    alert("Assignment Submitted Successfully!");
    setIsSubmitting(false);
    router.push("/student/feedback");
  }

  function navigateToQuestion(index: number) {
    setCurrentQuestionIndex(index);
    const nextQ = questions[index];

    // For mechanical problems, skip steps and go directly to final answer stage
    if (nextQ.problem_type === "problem_mechanical") {
      setCurrentStepInQuestion(nextQ.steps.length); // Set to final answer stage
    } else {
      setCurrentStepInQuestion(0); // Start from first step for socratic problems
    }

    setCurrentStepInput("");
    const processedStatement = nextQ.problemStatement
      .replace(/{vars\[0\]}/g, nextQ.variables[0])
      .replace(/{vars\[1\]}/g, nextQ.variables[1]);
    setChatMessages([
      {
        id: `${Date.now()}_${Math.random()}`,
        sender: "EDEN",
        text: `Let's work on Question ${index + 1}. ${processedStatement}`,
        timestamp: new Date(),
      },
    ]);
  }

  return (
    <ProtectedRoute>
      <div className={`h-screen w-full bg-gray-50 flex flex-col`}>
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-6 py-2.5 flex items-center justify-start flex-shrink-0">
          <div className="flex items-center gap-4">
            <Link
              href="/student/dashboard"
              className="text-gray-500 hover:text-gray-700"
            >
              <HomeIcon className="h-5 w-5" />
            </Link>
          </div>
          <h1 className="text-lg text-gray-500 text-center chatTitle">
            {assignment.title}
          </h1>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500 mr-4">
              Question {currentQuestionIndex + 1} of {totalQuestions}
            </span>
            <div className="flex items-center gap-1">
              {questions.map((q, idx) => (
                <button
                  key={q.id}
                  onClick={() => navigateToQuestion(idx)}
                  className={`
                    w-9 h-9 flex items-center justify-center font-bold text-lg border
                    ${
                      currentQuestionIndex === idx
                        ? "bg-blue-600 text-white scale-110 shadow-lg"
                        : "bg-gray-200 text-gray-700"
                    }
                    ${
                      q.problem_type === "socratic_conceptual"
                        ? "rounded"
                        : "rounded-full"
                    }
                    border-gray-300 transition-all
                    mx-1
                  `}
                  style={{
                    borderRadius:
                      q.problem_type === "socratic_conceptual"
                        ? "0.5rem"
                        : "999rem",
                  }}
                  aria-label={`Question ${idx + 1}`}
                >
                  {q.problem_type === "socratic_conceptual" ? "■" : "●"}
                </button>
              ))}
            </div>
            <div className="flex items-center gap-2 ml-4">
              <button
                onClick={() =>
                  navigateToQuestion(Math.max(0, currentQuestionIndex - 1))
                }
                disabled={currentQuestionIndex === 0}
                className="text-sm text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() =>
                  navigateToQuestion(
                    Math.min(totalQuestions - 1, currentQuestionIndex + 1)
                  )
                }
                disabled={currentQuestionIndex === totalQuestions - 1}
                className="text-sm text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        </header>
        {/* Main Content - Two Panes */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Pane: Completed Steps - Only show for socratic_conceptual problems */}
          {shouldShowSteps && (
            <div
              className={`fadeTab ${
                showStepSidebar ? "showingFade" : "hiddenFade"
              } bg-gray-100 border-l border-gray-200 p-3 overflow-y-auto flex-shrink-0`}
            >
              <div className="flex items-center gap-2 mb-4">
                <Bars3Icon
                  className="h-5 w-5 cursor-pointer select-none"
                  onClick={() => setShowStepSidebar((prev) => !prev)}
                />
                <CheckIcon className="h-5 w-5 text-green-600" />
                <h3 className="font-medium text-gray-800">Completed Steps</h3>
              </div>
              <p className="text-sm text-gray-500 mb-4">
                {Math.min(currentStepInQuestion, steps.length)} of{" "}
                {steps.length} steps completed
              </p>
              {steps.length === 0 && isFinalAnswerStage ? (
                <div className="text-center py-8">
                  <p className="text-sm text-gray-500">
                    No intermediate steps for this question.
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {steps
                    .slice(0, Math.min(currentStepInQuestion, steps.length))
                    .map((step) => (
                      <StepItem
                        key={step.id}
                        step={step}
                        isCompleted={true}
                        answer={
                          studentAnswers[currentQ.id]?.steps[step.id] || ""
                        }
                      />
                    ))}
                  {!isFinalAnswerStage && (
                    <StepItem
                      key={currentStep.id}
                      step={currentStep}
                      isCompleted={false}
                      answer={
                        studentAnswers[currentQ.id]?.steps[currentStep.id] || ""
                      }
                    />
                  )}
                </div>
              )}
            </div>
          )}
          {/* End Left Pane */}
          {/* Right Pane: Problem, Current Step, Chat */}
          <div className="flex-1 flex flex-col bg-white overflow-hidden">
            {/* Problem Statement Area */}
            <div className="flex flex-row bg-blue-800 px-5 py-2 flex-shrink-0 shadow-lg">
              {/* Problem from Database */}
              <div className="w-full">
                <div className="flex items-center gap-2 mb-1 text-white font-bold">
                  {!showStepSidebar && shouldShowSteps && (
                    <Bars3Icon
                      className="h-5 w-5 cursor-pointer select-none"
                      onClick={() => setShowStepSidebar((prev) => !prev)}
                    />
                  )}
                  {currentProblem ? currentProblem.title : "Loading Problem..."}
                </div>
                {currentProblem && (
                  <div className="bg-gray-50 rounded-lg px-2 py-1.5 mt-1 mb-1 min-h-18">
                    <div className="text-sm color-gray-900 font-medium">
                      {currentProblem.statement}
                    </div>
                  </div>
                )}

                {/* Answer Input Section */}
                {currentProblem && (
                  <div className="mt-2">
                    <div className="bg-white rounded-lg px-3 py-2 flex items-center gap-2">
                      <label className="text-sm font-medium text-gray-700">
                        Your Answer:
                      </label>
                      <input
                        type="text"
                        value={finalAnswer}
                        onChange={(e) => setFinalAnswer(e.target.value)}
                        placeholder="Enter your answer (e.g., x = 3)"
                        className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                      <button
                        onClick={handleAnswerValidation}
                        disabled={!finalAnswer.trim()}
                        className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
                      >
                        Check Answer
                      </button>
                    </div>

                    {/* Answer Validation Result */}
                    {answerValidation.message && (
                      <div
                        className={`mt-2 px-3 py-2 rounded-lg text-sm ${
                          answerValidation.isCorrect === true
                            ? "bg-green-100 text-green-800 border border-green-200"
                            : answerValidation.isCorrect === false
                            ? "bg-red-100 text-red-800 border border-red-200"
                            : "bg-yellow-100 text-yellow-800 border border-yellow-200"
                        }`}
                      >
                        {answerValidation.message}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Chat Messages Area (Scrollable) */}
            <div className="flex-1 overflow-y-auto px-6 py-3">
              <div className="max-w-screen mx-auto space-y-4">
                {chatMessages.map((msg) => (
                  <MessageComponent
                    msg={msg}
                    key={msg.id}
                    firstInitial={user?.email?.charAt(0).toUpperCase()}
                  />
                ))}
                <div ref={chatEndRef} />
              </div>
            </div>

            {/* Current Step Input / Final Answer Area */}
            <div className="px-6 py-2 border-t border-gray-200 flex-shrink-0">
              <div className="max-w-screen mx-auto">
                {/* Only show step input for socratic_conceptual problems */}
                {shouldShowSteps && !isFinalAnswerStage && currentStep && (
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      if (currentStepInput.trim()) handleStepSubmit();
                    }}
                    className="chatStepAnswer bg-blue-50 rounded-lg border border-blue-400 px-2 py-2 shadow-md flex flex-row gap-5 items-center"
                  >
                    <input
                      className="w-full pr-3 pl-1 rounded-lg border-none resize-none text-md focus:outline-none"
                      type="text"
                      value={currentStepInput}
                      placeholder={"Submit Step Answer "}
                      onChange={(e) => setCurrentStepInput(e.target.value)}
                      disabled={isSubmitting}
                    />
                    <button
                      className="px-2 py-2 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors disabled:opacity-50"
                      disabled={!currentStepInput.trim() || isSubmitting}
                      onClick={handleStepSubmit}
                      type="submit"
                    >
                      <ArrowUpIcon className="h-4.5 w-4.5" />
                    </button>
                  </form>
                )}

                {/* Show final answer section for socratic_conceptual when at final stage, or always for problem_mechanical */}
                {(shouldShowSteps && isFinalAnswerStage) || !shouldShowSteps ? (
                  <div className="bg-purple-50 rounded-lg border border-purple-200 p-2 shadow-md">
                    <div className="flex items-center gap-2 mb-1.5">
                      <div className="w-6 h-6 rounded-full bg-purple-600 text-white flex items-center justify-center font-bold text-sm">
                        ✓
                      </div>
                      <span className="font-medium text-gray-800 text-sm">
                        Final Answer
                      </span>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-1.5">
                      {finalVars.map((varName) => (
                        <div key={varName}>
                          <label className="block text-xs font-medium text-gray-700 mb-0.5">
                            {varName} =
                          </label>
                          <input
                            className="w-full px-2 py-1 border border-gray-200 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none text-sm"
                            placeholder={`Value for ${varName}`}
                            value={
                              studentAnswers[currentQ.id]?.final[varName] || ""
                            }
                            onChange={(e) =>
                              handleFinalChange(varName, e.target.value)
                            }
                            disabled={isSubmitting}
                          />
                        </div>
                      ))}
                    </div>
                    <button
                      className="w-full px-3 py-1.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 text-xs"
                      onClick={handleFinalSubmit}
                      disabled={isSubmitting}
                    >
                      {currentQuestionIndex === totalQuestions - 1
                        ? "Submit Assignment"
                        : "Next Question"}
                    </button>
                  </div>
                ) : null}
              </div>
            </div>

            {/* Chat Input Area */}
            <div className="px-6 mb-2 flex-shrink-0">
              <div className="max-w-screen mx-auto bg-gray-50 border border-gray-400 rounded-lg px-2 py-2 shadow-md">
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    if (chatInput.trim()) handleChatSend(chatInput);
                  }}
                  className="flex items-center gap-2 mb-1"
                >
                  <input
                    type="text"
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    placeholder="Ask Eden anything..."
                    disabled={chatLoading}
                    className="flex-1 pr-3 pl-1 focus:outline-none rounded-lg"
                  />
                  <button
                    type="submit"
                    disabled={chatLoading || !chatInput.trim()}
                    className="px-2 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors disabled:opacity-50"
                  >
                    <ArrowUpIcon className="h-4.5 w-4.5" />
                  </button>
                </form>
                <div className="flex gap-2 flex-wrap">
                  {quickPrompts.map((prompt) => (
                    <button
                      key={prompt.text}
                      onClick={() => handleChatSend(prompt.text)}
                      disabled={chatLoading}
                      className="px-3 py-1.5 bg-gray-50 border border-gray-200 hover:bg-gray-200 rounded-full text-xs text-gray-600 transition-colors"
                    >
                      <span className="mr-1">{prompt.icon}</span>
                      {prompt.text}
                    </button>
                  ))}
                </div>
              </div>
            </div>
            <p className="text-xs text-gray-500 mb-2 text-center">
              Your conversation with Eden is visible to your teacher. Keep your
              personal information private.
            </p>
          </div>{" "}
          {/* End Right Pane */}
        </div>{" "}
        {/* End Main Content - Two Panes */}
      </div>
    </ProtectedRoute>
  );
}
